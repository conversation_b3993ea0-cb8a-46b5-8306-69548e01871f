<!--
  *@name wenwenFeedback.vue
  *<AUTHOR>
  *@date 2025/7/29 10:38
-->
<template>
  <el-dialog
    title="反馈"
    :visible.sync="visible"
    v-if="visible"
    width="700px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form
      ref="feedbackForm"
      :model="feedbackForm"
      :rules="formRules"
      label-width="120px"
      class="feedback-form"
    >
      <!-- 反馈类型 -->
      <el-form-item label="反馈类型：" prop="feedback_type">
        <el-radio-group v-model="feedbackForm.feedback_type">
          <el-radio label="1">回答有误</el-radio>
          <el-radio label="2">响应慢</el-radio>
          <el-radio label="3">条例有误</el-radio>
          <el-radio label="4">法规有误</el-radio>
          <el-radio label="0">其它</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 用户姓名 -->
      <el-form-item label="用户姓名：" prop="feedback_name">
        <el-input
          v-model="feedbackForm.feedback_name"
          placeholder="请输入用户姓名"
        />
      </el-form-item>

      <!-- 联系方式 -->
      <el-form-item label="联系方式：" prop="telephone">
        <el-input
          v-model="feedbackForm.telephone"
          placeholder="请输入联系方式"
        />
      </el-form-item>

      <!-- 反馈内容 -->
      <el-form-item label="反馈内容：" prop="feedback_content">
        <el-input
          v-model="feedbackForm.feedback_content"
          type="textarea"
          :rows="4"
          placeholder="请输入反馈内容..."
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSubmit">提交反馈</el-button>
      <el-button @click="handleDirectDislike" type="text" class="direct-dislike-btn">
        直接踩
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "wenwenFeedback",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    feedbackData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      feedbackForm: {
        feedback_type: '1',
        feedback_name: '',
        telephone: '',
        feedback_content: ''
      },
      formRules: {
        feedback_type: [
          { required: true, message: '请选择反馈类型', trigger: 'change' }
        ],
        feedback_name: [
          { required: true, message: '请输入用户姓名', trigger: 'blur' }
        ],
        telephone: [
          { required: true, message: '请输入联系方式', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
        ],
        feedback_content: [
          { required: true, message: '请输入反馈内容', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 关闭弹窗
    handleClose() {
      this.$emit('close')
    },

    // 提交反馈
    handleSubmit() {
      this.$refs.feedbackForm.validate((valid) => {
        if (valid) {
          // 提交反馈数据
          this.$emit('submit-feedback', {
            ...this.feedbackForm,
            ...this.feedbackData
          })
          this.handleClose()
        } else {
          this.$message.warning('请完善必填信息')
          return false
        }
      })
    },

    // 直接踩
    handleDirectDislike() {
      this.$emit('direct-dislike', this.feedbackData)
      this.handleClose()
    }
  }
}
</script>

<style scoped lang="scss">
.feedback-form {
  .el-radio {
    margin-right: 15px;
    margin-bottom: 8px;
  }

  // 用户姓名和联系方式输入框样式
  :deep(.el-form-item:nth-child(2) .el-input__inner),
  :deep(.el-form-item:nth-child(3) .el-input__inner) {
    &:hover:not(.is-error) {
      border-color: #c1c1c1;
    }

    &:focus:not(.is-error) {
      border-color: #FE5461;
    }
  }

  // 校验错误状态下的样式优先级更高
  :deep(.el-form-item.is-error .el-input__inner) {
    &:hover {
      border-color: #F56C6C !important;
    }

    &:focus {
      border-color: #F56C6C !important;
    }
  }
}

.dialog-footer {
  text-align: right;

  .direct-dislike-btn {
    color: #CF1A1C;
    margin-right: auto;
    float: left;
  }
}
</style>